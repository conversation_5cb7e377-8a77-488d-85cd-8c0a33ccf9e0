# Swap Service Python 客户端

这是一个用于调用 Swap 服务的 Python 客户端，特别是 `SwapWithoutSign` 方法。

## 安装依赖

1. 确保你已经安装了 Python 3.7+

2. 安装所需的依赖包：
```bash
cd python_client
pip install -r requirements.txt
```

## 生成 Protobuf 文件

在使用客户端之前，需要先生成 Python 的 protobuf 文件：

```bash
cd python_client
python generate_proto.py
```

这将在 `generated/` 目录下生成必要的 protobuf 文件。

## 使用方法

### 基本用法

```python
from swap_client import SwapClient

# 创建客户端
client = SwapClient("localhost:50051", use_tls=False)

try:
    # 连接到服务器
    if client.connect():
        # 调用 SwapWithoutSign 方法
        tx_payload = client.swap_without_sign(
            signer_address="9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM",
            token="EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",  # USDC
            act=0,  # 0=Buy, 1=Sell
            amount="1000000",  # 1 USDC (6 decimals)
            slippage=500,  # 5% (基点)
            gas_fee="0.001",
            dex_name="raydium",
            chain_id=101  # Solana mainnet
        )
        
        if tx_payload:
            print(f"交易负载: {tx_payload}")
            
finally:
    client.close()
```

### 运行示例

直接运行客户端文件查看示例：

```bash
python swap_client.py
```

## 配置说明

### 服务器地址配置

根据你的 gRPC 服务器配置，修改连接参数：

```python
# 本地开发环境
client = SwapClient("localhost:50051", use_tls=False)

# 生产环境（使用TLS）
client = SwapClient("your-server.com:443", use_tls=True)
```

### 参数说明

#### SwapWithoutSign 方法参数

| 参数 | 类型 | 描述 | 示例 |
|------|------|------|------|
| `signer_address` | str | 签名地址 | "9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM" |
| `token` | str | 代币地址 | "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v" |
| `act` | int | 操作类型 | 0 (Buy) 或 1 (Sell) |
| `amount` | str | 交换金额 | "1000000" |
| `slippage` | int | 滑点（基点） | 500 (5%) |
| `gas_fee` | str | Gas费用 | "0.001" |
| `dex_name` | str | DEX名称 | "raydium" |
| `chain_id` | int | 链ID | 101 (Solana mainnet) |
| `jito_tip` | str | Jito小费 | "0" |
| `fee_address` | str | 费用地址 | "" |
| `fee_ratio` | int | 费用比率 | 0 |

## 错误处理

客户端包含完整的错误处理：

- 连接超时
- gRPC 错误
- 网络错误
- 参数验证错误

## 注意事项

1. 确保 gRPC 服务器正在运行
2. 检查网络连接和防火墙设置
3. 验证服务器地址和端口是否正确
4. 对于生产环境，建议使用 TLS 连接

## 文件结构

```
python_client/
├── README.md              # 说明文档
├── requirements.txt       # Python依赖
├── generate_proto.py      # 生成protobuf文件的脚本
├── swap_client.py         # 主要的客户端代码
└── generated/             # 生成的protobuf文件（运行generate_proto.py后创建）
    ├── __init__.py
    ├── swap_pb2.py
    └── swap_pb2_grpc.py
```
