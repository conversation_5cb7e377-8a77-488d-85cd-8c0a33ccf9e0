#!/usr/bin/env python3
"""
Swap服务的Python客户端
用于调用SwapWithoutSign方法
"""

import grpc
import sys
import os
from typing import Optional

# 添加生成的protobuf文件路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'generated'))

try:
    import swap_pb2
    import swap_pb2_grpc
except ImportError as e:
    print(f"错误: 无法导入protobuf文件: {e}")
    print("请先运行 generate_proto.py 生成protobuf文件")
    sys.exit(1)


class SwapClient:
    """Swap服务客户端"""
    
    def __init__(self, server_address: str = "localhost:50051", use_tls: bool = False):
        """
        初始化客户端
        
        Args:
            server_address: 服务器地址，格式为 "host:port"
            use_tls: 是否使用TLS连接
        """
        self.server_address = server_address
        self.use_tls = use_tls
        self.channel = None
        self.stub = None
        
    def connect(self):
        """连接到gRPC服务器"""
        try:
            if self.use_tls:
                # 使用TLS连接
                credentials = grpc.ssl_channel_credentials()
                self.channel = grpc.secure_channel(self.server_address, credentials)
            else:
                # 使用不安全连接
                self.channel = grpc.insecure_channel(self.server_address)
            
            # 创建stub
            self.stub = swap_pb2_grpc.SwapServiceStub(self.channel)
            
            # 测试连接
            grpc.channel_ready_future(self.channel).result(timeout=10)
            print(f"✓ 成功连接到服务器: {self.server_address}")
            return True
            
        except grpc.FutureTimeoutError:
            print(f"✗ 连接超时: {self.server_address}")
            return False
        except Exception as e:
            print(f"✗ 连接失败: {e}")
            return False
    
    def close(self):
        """关闭连接"""
        if self.channel:
            self.channel.close()
            print("连接已关闭")
    
    def swap_without_sign(self, 
                         signer_address: str,
                         token: str,
                         act: int,  # 0=Buy, 1=Sell
                         amount: str,
                         slippage: int = 500,  # 5%
                         gas_fee: str = "0.001",
                         dex_name: str = "raydium",
                         chain_id: int = 101,  # Solana mainnet
                         jito_tip: str = "0",
                         fee_address: str = "",
                         fee_ratio: int = 0) -> Optional[str]:
        """
        调用SwapWithoutSign方法
        
        Args:
            signer_address: 签名地址
            token: 代币地址
            act: 操作类型 (0=Buy, 1=Sell)
            amount: 交换金额
            slippage: 滑点 (基点，500 = 5%)
            gas_fee: Gas费用
            dex_name: DEX名称
            chain_id: 链ID
            jito_tip: Jito小费
            fee_address: 费用地址
            fee_ratio: 费用比率
            
        Returns:
            交易负载字符串，失败时返回None
        """
        if not self.stub:
            print("错误: 未连接到服务器")
            return None
        
        try:
            # 创建请求
            request = swap_pb2.SwapWithoutSignReq(
                signerAddress=signer_address,
                token=token,
                act=act,
                amount=amount,
                slippage=slippage,
                gasFee=gas_fee,
                dexName=dex_name,
                chainID=chain_id,
                jitoTip=jito_tip,
                feeAddress=fee_address,
                feeRatio=fee_ratio
            )
            
            print(f"发送SwapWithoutSign请求:")
            print(f"  签名地址: {signer_address}")
            print(f"  代币地址: {token}")
            print(f"  操作类型: {'Buy' if act == 0 else 'Sell'}")
            print(f"  金额: {amount}")
            print(f"  滑点: {slippage/100}%")
            print(f"  Gas费用: {gas_fee}")
            print(f"  DEX: {dex_name}")
            print(f"  链ID: {chain_id}")
            
            # 发送请求
            response = self.stub.SwapWithoutSign(request, timeout=30)
            
            print(f"✓ 请求成功")
            print(f"交易负载: {response.txPayload}")
            
            return response.txPayload
            
        except grpc.RpcError as e:
            print(f"✗ gRPC错误: {e.code()} - {e.details()}")
            return None
        except Exception as e:
            print(f"✗ 请求失败: {e}")
            return None
    
    def get_gas_fee(self):
        """获取Gas费用信息"""
        if not self.stub:
            print("错误: 未连接到服务器")
            return None
        
        try:
            request = swap_pb2.GetGasFeeReq()
            response = self.stub.GetGasFee(request, timeout=10)
            
            print("Gas费用信息:")
            for gas_fee in response.gasFee:
                print(f"  级别 {gas_fee.level}: {gas_fee.gasFee} ({gas_fee.gasFeeAmount})")
            
            return response.gasFee
            
        except grpc.RpcError as e:
            print(f"✗ gRPC错误: {e.code()} - {e.details()}")
            return None
        except Exception as e:
            print(f"✗ 请求失败: {e}")
            return None


def main():
    """主函数 - 示例用法"""
    # 创建客户端
    client = SwapClient("localhost:50051", use_tls=False)
    
    try:
        # 连接到服务器
        if not client.connect():
            return
        
        # 示例：获取Gas费用
        print("\n=== 获取Gas费用 ===")
        client.get_gas_fee()
        
        # 示例：调用SwapWithoutSign
        print("\n=== 调用SwapWithoutSign ===")
        tx_payload = client.swap_without_sign(
            signer_address="9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM",
            token="EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",  # USDC
            act=0,  # Buy
            amount="1000000",  # 1 USDC (6 decimals)
            slippage=500,  # 5%
            gas_fee="0.001",
            dex_name="raydium",
            chain_id=101
        )
        
        if tx_payload:
            print(f"\n成功获取交易负载，长度: {len(tx_payload)} 字符")
        
    finally:
        # 关闭连接
        client.close()


if __name__ == "__main__":
    main()
