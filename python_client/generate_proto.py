#!/usr/bin/env python3
"""
生成Python protobuf文件的脚本
"""

import os
import subprocess
import sys

def generate_python_proto():
    """生成Python protobuf文件"""

    # 确保输出目录存在
    output_dir = "python_client/generated"
    os.makedirs(output_dir, exist_ok=True)

    # proto文件路径
    proto_files = [
        "../trade/swap.proto",
        "../trade/key.proto"
    ]

    # 生成Python protobuf文件
    for proto_file in proto_files:
        if os.path.exists(proto_file):
            cmd = [
                "python3", "-m", "grpc_tools.protoc",
                f"--proto_path=../trade",
                f"--python_out={output_dir}",
                f"--grpc_python_out={output_dir}",
                proto_file
            ]

            print(f"生成 {proto_file} 的Python文件...")
            try:
                result = subprocess.run(cmd, check=True, capture_output=True, text=True)
                print(f"✓ 成功生成 {proto_file}")
            except subprocess.CalledProcessError as e:
                print(f"✗ 生成 {proto_file} 失败: {e}")
                print(f"错误输出: {e.stderr}")
                return False
        else:
            print(f"✗ Proto文件不存在: {proto_file}")
            return False

    # 创建__init__.py文件
    init_file = os.path.join(output_dir, "__init__.py")
    with open(init_file, "w") as f:
        f.write("# Generated protobuf files\n")

    print("✓ 所有protobuf文件生成完成")
    return True

if __name__ == "__main__":
    if generate_python_proto():
        print("Python protobuf文件生成成功！")
    else:
        print("Python protobuf文件生成失败！")
        sys.exit(1)
