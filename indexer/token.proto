syntax = "proto3";

package api.token.v1;

option go_package = "./;indexer";

service TokenService {

  //获取 token 的 top10，从节点获取
  rpc GetTop10 (GetTop10Req) returns (GetTop10Resp) {};

  //获取 token 的持有者数量，从 solscan 获取
  rpc GetHolders (GetHoldersReq) returns (GetHoldersResp) {};

  //批量获取 token 的持有者数量，从 solscan 获取
  rpc GetHoldersBatch (GetHoldersBatchReq) returns (GetHoldersBatchResp) {};

  //获取 token 的锁仓，从节点获取
  rpc GetLockedStatus (GetLockedStatusReq) returns (GetLockedStatusResp) {};
}

message GetTop10Req{
  string tokenAddress = 1;
}

message GetTop10Resp{
  //top10 百分比
  double top10 = 1;
}

message GetHoldersReq{
  string tokenAddress = 1;
}

message GetHoldersResp{
  uint64 holders = 1;
}

message GetHoldersBatchReq{
  repeated string tokenAddress = 1;
}

message GetHoldersBatchResp{
  repeated HoldersData holders = 1;
}

message HoldersData{
  string tokenAddress = 1;
  uint64 count = 2;
}

message GetLockedStatusReq{
  string poolAddress = 1;
}

message GetLockedStatusResp{
  //锁仓百分比
  double lockedStatus = 1;
}