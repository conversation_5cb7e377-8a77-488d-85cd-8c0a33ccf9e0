// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: token.proto

package indexer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetTop10Req struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TokenAddress  string                 `protobuf:"bytes,1,opt,name=tokenAddress,proto3" json:"tokenAddress,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTop10Req) Reset() {
	*x = GetTop10Req{}
	mi := &file_token_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTop10Req) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTop10Req) ProtoMessage() {}

func (x *GetTop10Req) ProtoReflect() protoreflect.Message {
	mi := &file_token_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTop10Req.ProtoReflect.Descriptor instead.
func (*GetTop10Req) Descriptor() ([]byte, []int) {
	return file_token_proto_rawDescGZIP(), []int{0}
}

func (x *GetTop10Req) GetTokenAddress() string {
	if x != nil {
		return x.TokenAddress
	}
	return ""
}

type GetTop10Resp struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// top10 百分比
	Top10         float64 `protobuf:"fixed64,1,opt,name=top10,proto3" json:"top10,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTop10Resp) Reset() {
	*x = GetTop10Resp{}
	mi := &file_token_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTop10Resp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTop10Resp) ProtoMessage() {}

func (x *GetTop10Resp) ProtoReflect() protoreflect.Message {
	mi := &file_token_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTop10Resp.ProtoReflect.Descriptor instead.
func (*GetTop10Resp) Descriptor() ([]byte, []int) {
	return file_token_proto_rawDescGZIP(), []int{1}
}

func (x *GetTop10Resp) GetTop10() float64 {
	if x != nil {
		return x.Top10
	}
	return 0
}

type GetHoldersReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TokenAddress  string                 `protobuf:"bytes,1,opt,name=tokenAddress,proto3" json:"tokenAddress,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetHoldersReq) Reset() {
	*x = GetHoldersReq{}
	mi := &file_token_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetHoldersReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetHoldersReq) ProtoMessage() {}

func (x *GetHoldersReq) ProtoReflect() protoreflect.Message {
	mi := &file_token_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetHoldersReq.ProtoReflect.Descriptor instead.
func (*GetHoldersReq) Descriptor() ([]byte, []int) {
	return file_token_proto_rawDescGZIP(), []int{2}
}

func (x *GetHoldersReq) GetTokenAddress() string {
	if x != nil {
		return x.TokenAddress
	}
	return ""
}

type GetHoldersResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Holders       uint64                 `protobuf:"varint,1,opt,name=holders,proto3" json:"holders,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetHoldersResp) Reset() {
	*x = GetHoldersResp{}
	mi := &file_token_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetHoldersResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetHoldersResp) ProtoMessage() {}

func (x *GetHoldersResp) ProtoReflect() protoreflect.Message {
	mi := &file_token_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetHoldersResp.ProtoReflect.Descriptor instead.
func (*GetHoldersResp) Descriptor() ([]byte, []int) {
	return file_token_proto_rawDescGZIP(), []int{3}
}

func (x *GetHoldersResp) GetHolders() uint64 {
	if x != nil {
		return x.Holders
	}
	return 0
}

type GetHoldersBatchReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TokenAddress  []string               `protobuf:"bytes,1,rep,name=tokenAddress,proto3" json:"tokenAddress,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetHoldersBatchReq) Reset() {
	*x = GetHoldersBatchReq{}
	mi := &file_token_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetHoldersBatchReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetHoldersBatchReq) ProtoMessage() {}

func (x *GetHoldersBatchReq) ProtoReflect() protoreflect.Message {
	mi := &file_token_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetHoldersBatchReq.ProtoReflect.Descriptor instead.
func (*GetHoldersBatchReq) Descriptor() ([]byte, []int) {
	return file_token_proto_rawDescGZIP(), []int{4}
}

func (x *GetHoldersBatchReq) GetTokenAddress() []string {
	if x != nil {
		return x.TokenAddress
	}
	return nil
}

type GetHoldersBatchResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Holders       []*HoldersData         `protobuf:"bytes,1,rep,name=holders,proto3" json:"holders,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetHoldersBatchResp) Reset() {
	*x = GetHoldersBatchResp{}
	mi := &file_token_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetHoldersBatchResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetHoldersBatchResp) ProtoMessage() {}

func (x *GetHoldersBatchResp) ProtoReflect() protoreflect.Message {
	mi := &file_token_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetHoldersBatchResp.ProtoReflect.Descriptor instead.
func (*GetHoldersBatchResp) Descriptor() ([]byte, []int) {
	return file_token_proto_rawDescGZIP(), []int{5}
}

func (x *GetHoldersBatchResp) GetHolders() []*HoldersData {
	if x != nil {
		return x.Holders
	}
	return nil
}

type HoldersData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TokenAddress  string                 `protobuf:"bytes,1,opt,name=tokenAddress,proto3" json:"tokenAddress,omitempty"`
	Count         uint64                 `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HoldersData) Reset() {
	*x = HoldersData{}
	mi := &file_token_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HoldersData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HoldersData) ProtoMessage() {}

func (x *HoldersData) ProtoReflect() protoreflect.Message {
	mi := &file_token_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HoldersData.ProtoReflect.Descriptor instead.
func (*HoldersData) Descriptor() ([]byte, []int) {
	return file_token_proto_rawDescGZIP(), []int{6}
}

func (x *HoldersData) GetTokenAddress() string {
	if x != nil {
		return x.TokenAddress
	}
	return ""
}

func (x *HoldersData) GetCount() uint64 {
	if x != nil {
		return x.Count
	}
	return 0
}

type GetLockedStatusReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PoolAddress   string                 `protobuf:"bytes,1,opt,name=poolAddress,proto3" json:"poolAddress,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetLockedStatusReq) Reset() {
	*x = GetLockedStatusReq{}
	mi := &file_token_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetLockedStatusReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLockedStatusReq) ProtoMessage() {}

func (x *GetLockedStatusReq) ProtoReflect() protoreflect.Message {
	mi := &file_token_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLockedStatusReq.ProtoReflect.Descriptor instead.
func (*GetLockedStatusReq) Descriptor() ([]byte, []int) {
	return file_token_proto_rawDescGZIP(), []int{7}
}

func (x *GetLockedStatusReq) GetPoolAddress() string {
	if x != nil {
		return x.PoolAddress
	}
	return ""
}

type GetLockedStatusResp struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 锁仓百分比
	LockedStatus  float64 `protobuf:"fixed64,1,opt,name=lockedStatus,proto3" json:"lockedStatus,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetLockedStatusResp) Reset() {
	*x = GetLockedStatusResp{}
	mi := &file_token_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetLockedStatusResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLockedStatusResp) ProtoMessage() {}

func (x *GetLockedStatusResp) ProtoReflect() protoreflect.Message {
	mi := &file_token_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLockedStatusResp.ProtoReflect.Descriptor instead.
func (*GetLockedStatusResp) Descriptor() ([]byte, []int) {
	return file_token_proto_rawDescGZIP(), []int{8}
}

func (x *GetLockedStatusResp) GetLockedStatus() float64 {
	if x != nil {
		return x.LockedStatus
	}
	return 0
}

var File_token_proto protoreflect.FileDescriptor

var file_token_proto_rawDesc = string([]byte{
	0x0a, 0x0b, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x2e, 0x76, 0x31, 0x22, 0x31, 0x0a, 0x0b, 0x47,
	0x65, 0x74, 0x54, 0x6f, 0x70, 0x31, 0x30, 0x52, 0x65, 0x71, 0x12, 0x22, 0x0a, 0x0c, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0x24,
	0x0a, 0x0c, 0x47, 0x65, 0x74, 0x54, 0x6f, 0x70, 0x31, 0x30, 0x52, 0x65, 0x73, 0x70, 0x12, 0x14,
	0x0a, 0x05, 0x74, 0x6f, 0x70, 0x31, 0x30, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x74,
	0x6f, 0x70, 0x31, 0x30, 0x22, 0x33, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x48, 0x6f, 0x6c, 0x64, 0x65,
	0x72, 0x73, 0x52, 0x65, 0x71, 0x12, 0x22, 0x0a, 0x0c, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0x2a, 0x0a, 0x0e, 0x47, 0x65, 0x74,
	0x48, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x18, 0x0a, 0x07, 0x68,
	0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x68, 0x6f,
	0x6c, 0x64, 0x65, 0x72, 0x73, 0x22, 0x38, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x48, 0x6f, 0x6c, 0x64,
	0x65, 0x72, 0x73, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x12, 0x22, 0x0a, 0x0c, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x0c, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22,
	0x4a, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x48, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x52, 0x65, 0x73, 0x70, 0x12, 0x33, 0x0a, 0x07, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x48, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73, 0x44, 0x61,
	0x74, 0x61, 0x52, 0x07, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73, 0x22, 0x47, 0x0a, 0x0b, 0x48,
	0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x22, 0x0a, 0x0c, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x14,
	0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x22, 0x36, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x63, 0x6b, 0x65,
	0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x6f,
	0x6f, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x70, 0x6f, 0x6f, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0x39, 0x0a, 0x13,
	0x47, 0x65, 0x74, 0x4c, 0x6f, 0x63, 0x6b, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x12, 0x22, 0x0a, 0x0c, 0x6c, 0x6f, 0x63, 0x6b, 0x65, 0x64, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x6c, 0x6f, 0x63, 0x6b, 0x65,
	0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x32, 0xd2, 0x02, 0x0a, 0x0c, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x43, 0x0a, 0x08, 0x47, 0x65, 0x74, 0x54,
	0x6f, 0x70, 0x31, 0x30, 0x12, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x6f, 0x70, 0x31, 0x30, 0x52, 0x65, 0x71, 0x1a,
	0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x54, 0x6f, 0x70, 0x31, 0x30, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x49, 0x0a,
	0x0a, 0x47, 0x65, 0x74, 0x48, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73, 0x12, 0x1b, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x48, 0x6f,
	0x6c, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x48, 0x6f, 0x6c, 0x64, 0x65,
	0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x58, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x48,
	0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73, 0x42, 0x61, 0x74, 0x63, 0x68, 0x12, 0x20, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x48, 0x6f,
	0x6c, 0x64, 0x65, 0x72, 0x73, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x1a, 0x21, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x48, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x73, 0x70,
	0x22, 0x00, 0x12, 0x58, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x63, 0x6b, 0x65, 0x64, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x63, 0x6b, 0x65, 0x64, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x63, 0x6b, 0x65, 0x64,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x42, 0x0c, 0x5a, 0x0a,
	0x2e, 0x2f, 0x3b, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
})

var (
	file_token_proto_rawDescOnce sync.Once
	file_token_proto_rawDescData []byte
)

func file_token_proto_rawDescGZIP() []byte {
	file_token_proto_rawDescOnce.Do(func() {
		file_token_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_token_proto_rawDesc), len(file_token_proto_rawDesc)))
	})
	return file_token_proto_rawDescData
}

var file_token_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_token_proto_goTypes = []any{
	(*GetTop10Req)(nil),         // 0: api.token.v1.GetTop10Req
	(*GetTop10Resp)(nil),        // 1: api.token.v1.GetTop10Resp
	(*GetHoldersReq)(nil),       // 2: api.token.v1.GetHoldersReq
	(*GetHoldersResp)(nil),      // 3: api.token.v1.GetHoldersResp
	(*GetHoldersBatchReq)(nil),  // 4: api.token.v1.GetHoldersBatchReq
	(*GetHoldersBatchResp)(nil), // 5: api.token.v1.GetHoldersBatchResp
	(*HoldersData)(nil),         // 6: api.token.v1.HoldersData
	(*GetLockedStatusReq)(nil),  // 7: api.token.v1.GetLockedStatusReq
	(*GetLockedStatusResp)(nil), // 8: api.token.v1.GetLockedStatusResp
}
var file_token_proto_depIdxs = []int32{
	6, // 0: api.token.v1.GetHoldersBatchResp.holders:type_name -> api.token.v1.HoldersData
	0, // 1: api.token.v1.TokenService.GetTop10:input_type -> api.token.v1.GetTop10Req
	2, // 2: api.token.v1.TokenService.GetHolders:input_type -> api.token.v1.GetHoldersReq
	4, // 3: api.token.v1.TokenService.GetHoldersBatch:input_type -> api.token.v1.GetHoldersBatchReq
	7, // 4: api.token.v1.TokenService.GetLockedStatus:input_type -> api.token.v1.GetLockedStatusReq
	1, // 5: api.token.v1.TokenService.GetTop10:output_type -> api.token.v1.GetTop10Resp
	3, // 6: api.token.v1.TokenService.GetHolders:output_type -> api.token.v1.GetHoldersResp
	5, // 7: api.token.v1.TokenService.GetHoldersBatch:output_type -> api.token.v1.GetHoldersBatchResp
	8, // 8: api.token.v1.TokenService.GetLockedStatus:output_type -> api.token.v1.GetLockedStatusResp
	5, // [5:9] is the sub-list for method output_type
	1, // [1:5] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_token_proto_init() }
func file_token_proto_init() {
	if File_token_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_token_proto_rawDesc), len(file_token_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_token_proto_goTypes,
		DependencyIndexes: file_token_proto_depIdxs,
		MessageInfos:      file_token_proto_msgTypes,
	}.Build()
	File_token_proto = out.File
	file_token_proto_goTypes = nil
	file_token_proto_depIdxs = nil
}
