// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: token.proto

package indexer

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	TokenService_GetTop10_FullMethodName        = "/api.token.v1.TokenService/GetTop10"
	TokenService_GetHolders_FullMethodName      = "/api.token.v1.TokenService/GetHolders"
	TokenService_GetHoldersBatch_FullMethodName = "/api.token.v1.TokenService/GetHoldersBatch"
	TokenService_GetLockedStatus_FullMethodName = "/api.token.v1.TokenService/GetLockedStatus"
)

// TokenServiceClient is the client API for TokenService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TokenServiceClient interface {
	// 获取 token 的 top10，从节点获取
	GetTop10(ctx context.Context, in *GetTop10Req, opts ...grpc.CallOption) (*GetTop10Resp, error)
	// 获取 token 的持有者数量，从 solscan 获取
	GetHolders(ctx context.Context, in *GetHoldersReq, opts ...grpc.CallOption) (*GetHoldersResp, error)
	// 批量获取 token 的持有者数量，从 solscan 获取
	GetHoldersBatch(ctx context.Context, in *GetHoldersBatchReq, opts ...grpc.CallOption) (*GetHoldersBatchResp, error)
	// 获取 token 的锁仓，从节点获取
	GetLockedStatus(ctx context.Context, in *GetLockedStatusReq, opts ...grpc.CallOption) (*GetLockedStatusResp, error)
}

type tokenServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewTokenServiceClient(cc grpc.ClientConnInterface) TokenServiceClient {
	return &tokenServiceClient{cc}
}

func (c *tokenServiceClient) GetTop10(ctx context.Context, in *GetTop10Req, opts ...grpc.CallOption) (*GetTop10Resp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetTop10Resp)
	err := c.cc.Invoke(ctx, TokenService_GetTop10_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tokenServiceClient) GetHolders(ctx context.Context, in *GetHoldersReq, opts ...grpc.CallOption) (*GetHoldersResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetHoldersResp)
	err := c.cc.Invoke(ctx, TokenService_GetHolders_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tokenServiceClient) GetHoldersBatch(ctx context.Context, in *GetHoldersBatchReq, opts ...grpc.CallOption) (*GetHoldersBatchResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetHoldersBatchResp)
	err := c.cc.Invoke(ctx, TokenService_GetHoldersBatch_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tokenServiceClient) GetLockedStatus(ctx context.Context, in *GetLockedStatusReq, opts ...grpc.CallOption) (*GetLockedStatusResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetLockedStatusResp)
	err := c.cc.Invoke(ctx, TokenService_GetLockedStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TokenServiceServer is the server API for TokenService service.
// All implementations must embed UnimplementedTokenServiceServer
// for forward compatibility.
type TokenServiceServer interface {
	// 获取 token 的 top10，从节点获取
	GetTop10(context.Context, *GetTop10Req) (*GetTop10Resp, error)
	// 获取 token 的持有者数量，从 solscan 获取
	GetHolders(context.Context, *GetHoldersReq) (*GetHoldersResp, error)
	// 批量获取 token 的持有者数量，从 solscan 获取
	GetHoldersBatch(context.Context, *GetHoldersBatchReq) (*GetHoldersBatchResp, error)
	// 获取 token 的锁仓，从节点获取
	GetLockedStatus(context.Context, *GetLockedStatusReq) (*GetLockedStatusResp, error)
	mustEmbedUnimplementedTokenServiceServer()
}

// UnimplementedTokenServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedTokenServiceServer struct{}

func (UnimplementedTokenServiceServer) GetTop10(context.Context, *GetTop10Req) (*GetTop10Resp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTop10 not implemented")
}
func (UnimplementedTokenServiceServer) GetHolders(context.Context, *GetHoldersReq) (*GetHoldersResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetHolders not implemented")
}
func (UnimplementedTokenServiceServer) GetHoldersBatch(context.Context, *GetHoldersBatchReq) (*GetHoldersBatchResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetHoldersBatch not implemented")
}
func (UnimplementedTokenServiceServer) GetLockedStatus(context.Context, *GetLockedStatusReq) (*GetLockedStatusResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLockedStatus not implemented")
}
func (UnimplementedTokenServiceServer) mustEmbedUnimplementedTokenServiceServer() {}
func (UnimplementedTokenServiceServer) testEmbeddedByValue()                      {}

// UnsafeTokenServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TokenServiceServer will
// result in compilation errors.
type UnsafeTokenServiceServer interface {
	mustEmbedUnimplementedTokenServiceServer()
}

func RegisterTokenServiceServer(s grpc.ServiceRegistrar, srv TokenServiceServer) {
	// If the following call pancis, it indicates UnimplementedTokenServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&TokenService_ServiceDesc, srv)
}

func _TokenService_GetTop10_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTop10Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TokenServiceServer).GetTop10(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TokenService_GetTop10_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TokenServiceServer).GetTop10(ctx, req.(*GetTop10Req))
	}
	return interceptor(ctx, in, info, handler)
}

func _TokenService_GetHolders_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetHoldersReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TokenServiceServer).GetHolders(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TokenService_GetHolders_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TokenServiceServer).GetHolders(ctx, req.(*GetHoldersReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TokenService_GetHoldersBatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetHoldersBatchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TokenServiceServer).GetHoldersBatch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TokenService_GetHoldersBatch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TokenServiceServer).GetHoldersBatch(ctx, req.(*GetHoldersBatchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TokenService_GetLockedStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLockedStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TokenServiceServer).GetLockedStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TokenService_GetLockedStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TokenServiceServer).GetLockedStatus(ctx, req.(*GetLockedStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

// TokenService_ServiceDesc is the grpc.ServiceDesc for TokenService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TokenService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.token.v1.TokenService",
	HandlerType: (*TokenServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetTop10",
			Handler:    _TokenService_GetTop10_Handler,
		},
		{
			MethodName: "GetHolders",
			Handler:    _TokenService_GetHolders_Handler,
		},
		{
			MethodName: "GetHoldersBatch",
			Handler:    _TokenService_GetHoldersBatch_Handler,
		},
		{
			MethodName: "GetLockedStatus",
			Handler:    _TokenService_GetLockedStatus_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "token.proto",
}
