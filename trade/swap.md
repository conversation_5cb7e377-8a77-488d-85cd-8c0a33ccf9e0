# Swap Service API Documentation

## Overview

This document describes the Protocol Buffer definitions for the Swap Service API. The service provides functionality for cryptocurrency token swapping operations.

## Service Definition

The `SwapService` provides the following RPC methods:

| Method | Description |
|--------|-------------|
| `GetGasFee` | Retrieves gas fee information |
| `Swap` | Performs a swap operation with signature and broadcasting, returns transaction hash |
| `SimulateSwap` | Simulates a swap transaction and returns the expected result |
| `SwapWithoutSign` | Builds a transaction without signing, returns transaction payload |

## Data Structures

### Enumerations

#### SwapAct

Defines the type of swap action:

| Value | Description |
|-------|-------------|
| `Buy` | Buy operation (value = 0) |
| `Sell` | Sell operation (value = 1) |

### Messages

#### SimulateSwapReq

Request for simulating a swap operation:

| Field | Type | Description |
|-------|------|-------------|
| `tokenAddress` | string | Address of the token to swap |
| `amountIn` | string | Input amount for the swap |
| `isBuy` | bool | Whether this is a buy operation |
| `chainID` | uint64 | ID of the blockchain |

#### SimulateSwapResp

Response from a simulated swap:

| Field | Type | Description |
|-------|------|-------------|
| `amountOut` | string | Expected output amount from the swap |

#### GetGasFeeReq

Empty request for retrieving gas fee information.

#### GetGasFeeResp

Response containing gas fee information:

| Field | Type | Description |
|-------|------|-------------|
| `gasFee` | repeated GasFee | List of gas fee options |

#### GasFee

Gas fee information:

| Field | Type | Description |
|-------|------|-------------|
| `level` | int64 | Fee level |
| `gasFee` | double | Gas fee value |
| `gasFeeAmount` | string | Gas fee amount as string |
| `value` | double | Value representation |

#### SwapReq

Request for performing a swap with signature:

| Field | Type | Description |
|-------|------|-------------|
| `signerIndex` | int64 | Index of the signing address |
| `signerAddress` | string | Address used for signing |
| `token` | string | Token address |
| `act` | SwapAct | Action type (Buy/Sell) |
| `amount` | string | Amount to swap |
| `slippage` | uint64 | Allowed slippage |
| `gasFee` | string | Gas fee |
| `dexName` | string | Name of the decentralized exchange |
| `chainID` | uint64 | ID of the blockchain |
| `jitoTip` | string | Jito tip |
| `feeAddress` | string | Fee address |
| `feeRatio` | uint64 | Fee ratio |

#### SwapResp

Response from a swap operation:

| Field | Type | Description |
|-------|------|-------------|
| `txHash` | string | Transaction hash |

#### SwapWithoutSignReq

Request for building a swap transaction without signing:

| Field | Type | Description |
|-------|------|-------------|
| `signerAddress` | string | Address used for signing |
| `token` | string | Token address |
| `act` | SwapAct | Action type (Buy/Sell) |
| `amount` | string | Amount to swap |
| `slippage` | uint64 | Allowed slippage |
| `gasFee` | string | Gas fee |
| `dexName` | string | Name of the decentralized exchange |
| `chainID` | uint64 | ID of the blockchain |
| `jitoTip` | string | Jito tip |
| `feeAddress` | string | Fee address |
| `feeRatio` | uint64 | Fee ratio |

#### SwapWithoutSignResp

Response from building a swap transaction without signing:

| Field | Type | Description |
|-------|------|-------------|
| `txPayload` | string | Transaction payload |

## Package Information

- **Package**: `api.swap.v1`
- **Go Package**: `./;trade`
