# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: trade/swap.proto
# Protobuf Python Version: 5.29.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    29,
    1,
    '',
    'trade/swap.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x10trade/swap.proto\x12\x0b\x61pi.swap.v1\"Y\n\x0fSimulateSwapReq\x12\x14\n\x0ctokenAddress\x18\x01 \x01(\t\x12\x10\n\x08\x61mountIn\x18\x02 \x01(\t\x12\r\n\x05isBuy\x18\x03 \x01(\x08\x12\x0f\n\x07\x63hainID\x18\x04 \x01(\x04\"%\n\x10SimulateSwapResp\x12\x11\n\tamountOut\x18\x01 \x01(\t\"\x0e\n\x0cGetGasFeeReq\"4\n\rGetGasFeeResp\x12#\n\x06gasFee\x18\x01 \x03(\x0b\x32\x13.api.swap.v1.GasFee\"L\n\x06GasFee\x12\r\n\x05level\x18\x01 \x01(\x03\x12\x0e\n\x06gasFee\x18\x02 \x01(\x01\x12\x14\n\x0cgasFeeAmount\x18\x03 \x01(\t\x12\r\n\x05value\x18\x04 \x01(\x01\"\x83\x02\n\x07SwapReq\x12\x13\n\x0bsignerIndex\x18\x01 \x01(\x03\x12\x15\n\rsignerAddress\x18\x02 \x01(\t\x12\r\n\x05token\x18\x03 \x01(\t\x12!\n\x03\x61\x63t\x18\x04 \x01(\x0e\x32\x14.api.swap.v1.SwapAct\x12\x0e\n\x06\x61mount\x18\x05 \x01(\t\x12\x10\n\x08slippage\x18\x06 \x01(\x04\x12\x0e\n\x06gasFee\x18\x07 \x01(\t\x12\x0f\n\x07\x64\x65xName\x18\x08 \x01(\t\x12\x0f\n\x07\x63hainID\x18\t \x01(\x04\x12\x0f\n\x07jitoTip\x18\n \x01(\t\x12\x12\n\nfeeAddress\x18\x0b \x01(\t\x12\x10\n\x08\x66\x65\x65Ratio\x18\x0c \x01(\x04\x12\x0f\n\x07orderId\x18\r \x01(\t\"+\n\x08SwapResp\x12\x0e\n\x06txHash\x18\x01 \x01(\t\x12\x0f\n\x07orderId\x18\x02 \x01(\t\"\xe8\x01\n\x12SwapWithoutSignReq\x12\x15\n\rsignerAddress\x18\x01 \x01(\t\x12\r\n\x05token\x18\x02 \x01(\t\x12!\n\x03\x61\x63t\x18\x03 \x01(\x0e\x32\x14.api.swap.v1.SwapAct\x12\x0e\n\x06\x61mount\x18\x04 \x01(\t\x12\x10\n\x08slippage\x18\x05 \x01(\x04\x12\x0e\n\x06gasFee\x18\x06 \x01(\t\x12\x0f\n\x07\x64\x65xName\x18\x07 \x01(\t\x12\x0f\n\x07\x63hainID\x18\x08 \x01(\x04\x12\x0f\n\x07jitoTip\x18\t \x01(\t\x12\x12\n\nfeeAddress\x18\n \x01(\t\x12\x10\n\x08\x66\x65\x65Ratio\x18\x0b \x01(\x04\"(\n\x13SwapWithoutSignResp\x12\x11\n\ttxPayload\x18\x01 \x01(\t\"/\n\x0eGetTxResultReq\x12\x0f\n\x07\x63hainId\x18\x01 \x01(\x04\x12\x0c\n\x04hash\x18\x02 \x01(\t\"\xd2\x02\n\x0fGetTxResultResp\x12\x0f\n\x07Success\x18\x01 \x01(\x08\x12\x0c\n\x04Hash\x18\x02 \x01(\t\x12\x0e\n\x06Signer\x18\x03 \x01(\t\x12\x0f\n\x07\x42\x61seFee\x18\x04 \x01(\x04\x12\x13\n\x0bPriorityFee\x18\x05 \x01(\x04\x12\x0f\n\x07JitoFee\x18\x06 \x01(\x04\x12\x11\n\tEvmGasFee\x18\x07 \x01(\t\x12\r\n\x05IsBuy\x18\x08 \x01(\x08\x12\x13\n\x0b\x42\x61seAddress\x18\t \x01(\t\x12\x12\n\nBaseAmount\x18\n \x01(\t\x12\x14\n\x0cQuoteAddress\x18\x0b \x01(\t\x12\x13\n\x0bQuoteAmount\x18\x0c \x01(\t\x12\x13\n\x0b\x42\x61seDecimal\x18\r \x01(\r\x12\x14\n\x0cQuoteDecimal\x18\x0e \x01(\r\x12\r\n\x05Price\x18\x0f \x01(\t\x12\x0c\n\x04Rent\x18\x10 \x01(\x03\x12\n\n\x02Ts\x18\x11 \x01(\x03\x12\x0f\n\x07\x43hainId\x18\x12 \x01(\x04*\x1c\n\x07SwapAct\x12\x07\n\x03\x42uy\x10\x00\x12\x08\n\x04Sell\x10\x01\x32\xfd\x02\n\x0bSwapService\x12\x44\n\tGetGasFee\x12\x19.api.swap.v1.GetGasFeeReq\x1a\x1a.api.swap.v1.GetGasFeeResp\"\x00\x12\x35\n\x04Swap\x12\x14.api.swap.v1.SwapReq\x1a\x15.api.swap.v1.SwapResp\"\x00\x12M\n\x0cSimulateSwap\x12\x1c.api.swap.v1.SimulateSwapReq\x1a\x1d.api.swap.v1.SimulateSwapResp\"\x00\x12V\n\x0fSwapWithoutSign\x12\x1f.api.swap.v1.SwapWithoutSignReq\x1a .api.swap.v1.SwapWithoutSignResp\"\x00\x12J\n\x0bGetTxResult\x12\x1b.api.swap.v1.GetTxResultReq\x1a\x1c.api.swap.v1.GetTxResultResp\"\x00\x42\nZ\x08./;tradeb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'trade.swap_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z\010./;trade'
  _globals['_SWAPACT']._serialized_start=1285
  _globals['_SWAPACT']._serialized_end=1313
  _globals['_SIMULATESWAPREQ']._serialized_start=33
  _globals['_SIMULATESWAPREQ']._serialized_end=122
  _globals['_SIMULATESWAPRESP']._serialized_start=124
  _globals['_SIMULATESWAPRESP']._serialized_end=161
  _globals['_GETGASFEEREQ']._serialized_start=163
  _globals['_GETGASFEEREQ']._serialized_end=177
  _globals['_GETGASFEERESP']._serialized_start=179
  _globals['_GETGASFEERESP']._serialized_end=231
  _globals['_GASFEE']._serialized_start=233
  _globals['_GASFEE']._serialized_end=309
  _globals['_SWAPREQ']._serialized_start=312
  _globals['_SWAPREQ']._serialized_end=571
  _globals['_SWAPRESP']._serialized_start=573
  _globals['_SWAPRESP']._serialized_end=616
  _globals['_SWAPWITHOUTSIGNREQ']._serialized_start=619
  _globals['_SWAPWITHOUTSIGNREQ']._serialized_end=851
  _globals['_SWAPWITHOUTSIGNRESP']._serialized_start=853
  _globals['_SWAPWITHOUTSIGNRESP']._serialized_end=893
  _globals['_GETTXRESULTREQ']._serialized_start=895
  _globals['_GETTXRESULTREQ']._serialized_end=942
  _globals['_GETTXRESULTRESP']._serialized_start=945
  _globals['_GETTXRESULTRESP']._serialized_end=1283
  _globals['_SWAPSERVICE']._serialized_start=1316
  _globals['_SWAPSERVICE']._serialized_end=1697
# @@protoc_insertion_point(module_scope)
