// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: swap.proto

package trade

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SwapAct int32

const (
	SwapAct_Buy  SwapAct = 0
	SwapAct_Sell SwapAct = 1
)

// Enum value maps for SwapAct.
var (
	SwapAct_name = map[int32]string{
		0: "Buy",
		1: "Sell",
	}
	SwapAct_value = map[string]int32{
		"Buy":  0,
		"Sell": 1,
	}
)

func (x SwapAct) Enum() *SwapAct {
	p := new(SwapAct)
	*p = x
	return p
}

func (x SwapAct) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SwapAct) Descriptor() protoreflect.EnumDescriptor {
	return file_swap_proto_enumTypes[0].Descriptor()
}

func (SwapAct) Type() protoreflect.EnumType {
	return &file_swap_proto_enumTypes[0]
}

func (x SwapAct) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SwapAct.Descriptor instead.
func (SwapAct) EnumDescriptor() ([]byte, []int) {
	return file_swap_proto_rawDescGZIP(), []int{0}
}

type SimulateSwapReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TokenAddress  string                 `protobuf:"bytes,1,opt,name=tokenAddress,proto3" json:"tokenAddress,omitempty"`
	AmountIn      string                 `protobuf:"bytes,2,opt,name=amountIn,proto3" json:"amountIn,omitempty"`
	IsBuy         bool                   `protobuf:"varint,3,opt,name=isBuy,proto3" json:"isBuy,omitempty"`
	ChainID       uint64                 `protobuf:"varint,4,opt,name=chainID,proto3" json:"chainID,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SimulateSwapReq) Reset() {
	*x = SimulateSwapReq{}
	mi := &file_swap_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SimulateSwapReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SimulateSwapReq) ProtoMessage() {}

func (x *SimulateSwapReq) ProtoReflect() protoreflect.Message {
	mi := &file_swap_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SimulateSwapReq.ProtoReflect.Descriptor instead.
func (*SimulateSwapReq) Descriptor() ([]byte, []int) {
	return file_swap_proto_rawDescGZIP(), []int{0}
}

func (x *SimulateSwapReq) GetTokenAddress() string {
	if x != nil {
		return x.TokenAddress
	}
	return ""
}

func (x *SimulateSwapReq) GetAmountIn() string {
	if x != nil {
		return x.AmountIn
	}
	return ""
}

func (x *SimulateSwapReq) GetIsBuy() bool {
	if x != nil {
		return x.IsBuy
	}
	return false
}

func (x *SimulateSwapReq) GetChainID() uint64 {
	if x != nil {
		return x.ChainID
	}
	return 0
}

type SimulateSwapResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AmountOut     string                 `protobuf:"bytes,1,opt,name=amountOut,proto3" json:"amountOut,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SimulateSwapResp) Reset() {
	*x = SimulateSwapResp{}
	mi := &file_swap_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SimulateSwapResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SimulateSwapResp) ProtoMessage() {}

func (x *SimulateSwapResp) ProtoReflect() protoreflect.Message {
	mi := &file_swap_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SimulateSwapResp.ProtoReflect.Descriptor instead.
func (*SimulateSwapResp) Descriptor() ([]byte, []int) {
	return file_swap_proto_rawDescGZIP(), []int{1}
}

func (x *SimulateSwapResp) GetAmountOut() string {
	if x != nil {
		return x.AmountOut
	}
	return ""
}

type GetGasFeeReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetGasFeeReq) Reset() {
	*x = GetGasFeeReq{}
	mi := &file_swap_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetGasFeeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGasFeeReq) ProtoMessage() {}

func (x *GetGasFeeReq) ProtoReflect() protoreflect.Message {
	mi := &file_swap_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGasFeeReq.ProtoReflect.Descriptor instead.
func (*GetGasFeeReq) Descriptor() ([]byte, []int) {
	return file_swap_proto_rawDescGZIP(), []int{2}
}

type GetGasFeeResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	GasFee        []*GasFee              `protobuf:"bytes,1,rep,name=gasFee,proto3" json:"gasFee,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetGasFeeResp) Reset() {
	*x = GetGasFeeResp{}
	mi := &file_swap_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetGasFeeResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGasFeeResp) ProtoMessage() {}

func (x *GetGasFeeResp) ProtoReflect() protoreflect.Message {
	mi := &file_swap_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGasFeeResp.ProtoReflect.Descriptor instead.
func (*GetGasFeeResp) Descriptor() ([]byte, []int) {
	return file_swap_proto_rawDescGZIP(), []int{3}
}

func (x *GetGasFeeResp) GetGasFee() []*GasFee {
	if x != nil {
		return x.GasFee
	}
	return nil
}

type GasFee struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Level         int64                  `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	GasFee        float64                `protobuf:"fixed64,2,opt,name=gasFee,proto3" json:"gasFee,omitempty"`
	GasFeeAmount  string                 `protobuf:"bytes,3,opt,name=gasFeeAmount,proto3" json:"gasFeeAmount,omitempty"`
	Value         float64                `protobuf:"fixed64,4,opt,name=value,proto3" json:"value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GasFee) Reset() {
	*x = GasFee{}
	mi := &file_swap_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GasFee) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GasFee) ProtoMessage() {}

func (x *GasFee) ProtoReflect() protoreflect.Message {
	mi := &file_swap_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GasFee.ProtoReflect.Descriptor instead.
func (*GasFee) Descriptor() ([]byte, []int) {
	return file_swap_proto_rawDescGZIP(), []int{4}
}

func (x *GasFee) GetLevel() int64 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *GasFee) GetGasFee() float64 {
	if x != nil {
		return x.GasFee
	}
	return 0
}

func (x *GasFee) GetGasFeeAmount() string {
	if x != nil {
		return x.GasFeeAmount
	}
	return ""
}

func (x *GasFee) GetValue() float64 {
	if x != nil {
		return x.Value
	}
	return 0
}

type SwapReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SignerIndex   int64                  `protobuf:"varint,1,opt,name=signerIndex,proto3" json:"signerIndex,omitempty"`          //签名地址的 index
	SignerAddress string                 `protobuf:"bytes,2,opt,name=signerAddress,proto3" json:"signerAddress,omitempty"`       //签名地址
	Token         string                 `protobuf:"bytes,3,opt,name=token,proto3" json:"token,omitempty"`                       //token 地址
	Act           SwapAct                `protobuf:"varint,4,opt,name=act,proto3,enum=api.swap.v1.SwapAct" json:"act,omitempty"` //动作
	Amount        string                 `protobuf:"bytes,5,opt,name=amount,proto3" json:"amount,omitempty"`                     //数量
	Slippage      uint64                 `protobuf:"varint,6,opt,name=slippage,proto3" json:"slippage,omitempty"`
	GasFee        string                 `protobuf:"bytes,7,opt,name=gasFee,proto3" json:"gasFee,omitempty"`
	DexName       string                 `protobuf:"bytes,8,opt,name=dexName,proto3" json:"dexName,omitempty"`
	ChainID       uint64                 `protobuf:"varint,9,opt,name=chainID,proto3" json:"chainID,omitempty"`
	JitoTip       string                 `protobuf:"bytes,10,opt,name=jitoTip,proto3" json:"jitoTip,omitempty"`
	FeeAddress    string                 `protobuf:"bytes,11,opt,name=feeAddress,proto3" json:"feeAddress,omitempty"`
	FeeRatio      uint64                 `protobuf:"varint,12,opt,name=feeRatio,proto3" json:"feeRatio,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SwapReq) Reset() {
	*x = SwapReq{}
	mi := &file_swap_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SwapReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SwapReq) ProtoMessage() {}

func (x *SwapReq) ProtoReflect() protoreflect.Message {
	mi := &file_swap_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SwapReq.ProtoReflect.Descriptor instead.
func (*SwapReq) Descriptor() ([]byte, []int) {
	return file_swap_proto_rawDescGZIP(), []int{5}
}

func (x *SwapReq) GetSignerIndex() int64 {
	if x != nil {
		return x.SignerIndex
	}
	return 0
}

func (x *SwapReq) GetSignerAddress() string {
	if x != nil {
		return x.SignerAddress
	}
	return ""
}

func (x *SwapReq) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *SwapReq) GetAct() SwapAct {
	if x != nil {
		return x.Act
	}
	return SwapAct_Buy
}

func (x *SwapReq) GetAmount() string {
	if x != nil {
		return x.Amount
	}
	return ""
}

func (x *SwapReq) GetSlippage() uint64 {
	if x != nil {
		return x.Slippage
	}
	return 0
}

func (x *SwapReq) GetGasFee() string {
	if x != nil {
		return x.GasFee
	}
	return ""
}

func (x *SwapReq) GetDexName() string {
	if x != nil {
		return x.DexName
	}
	return ""
}

func (x *SwapReq) GetChainID() uint64 {
	if x != nil {
		return x.ChainID
	}
	return 0
}

func (x *SwapReq) GetJitoTip() string {
	if x != nil {
		return x.JitoTip
	}
	return ""
}

func (x *SwapReq) GetFeeAddress() string {
	if x != nil {
		return x.FeeAddress
	}
	return ""
}

func (x *SwapReq) GetFeeRatio() uint64 {
	if x != nil {
		return x.FeeRatio
	}
	return 0
}

type SwapResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TxHash        string                 `protobuf:"bytes,1,opt,name=txHash,proto3" json:"txHash,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SwapResp) Reset() {
	*x = SwapResp{}
	mi := &file_swap_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SwapResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SwapResp) ProtoMessage() {}

func (x *SwapResp) ProtoReflect() protoreflect.Message {
	mi := &file_swap_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SwapResp.ProtoReflect.Descriptor instead.
func (*SwapResp) Descriptor() ([]byte, []int) {
	return file_swap_proto_rawDescGZIP(), []int{6}
}

func (x *SwapResp) GetTxHash() string {
	if x != nil {
		return x.TxHash
	}
	return ""
}

type SwapWithoutSignReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SignerAddress string                 `protobuf:"bytes,1,opt,name=signerAddress,proto3" json:"signerAddress,omitempty"`       //签名地址
	Token         string                 `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`                       //token 地址
	Act           SwapAct                `protobuf:"varint,3,opt,name=act,proto3,enum=api.swap.v1.SwapAct" json:"act,omitempty"` //动作
	Amount        string                 `protobuf:"bytes,4,opt,name=amount,proto3" json:"amount,omitempty"`                     //数量
	Slippage      uint64                 `protobuf:"varint,5,opt,name=slippage,proto3" json:"slippage,omitempty"`
	GasFee        string                 `protobuf:"bytes,6,opt,name=gasFee,proto3" json:"gasFee,omitempty"`
	DexName       string                 `protobuf:"bytes,7,opt,name=dexName,proto3" json:"dexName,omitempty"`
	ChainID       uint64                 `protobuf:"varint,8,opt,name=chainID,proto3" json:"chainID,omitempty"`
	JitoTip       string                 `protobuf:"bytes,9,opt,name=jitoTip,proto3" json:"jitoTip,omitempty"`
	FeeAddress    string                 `protobuf:"bytes,10,opt,name=feeAddress,proto3" json:"feeAddress,omitempty"`
	FeeRatio      uint64                 `protobuf:"varint,11,opt,name=feeRatio,proto3" json:"feeRatio,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SwapWithoutSignReq) Reset() {
	*x = SwapWithoutSignReq{}
	mi := &file_swap_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SwapWithoutSignReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SwapWithoutSignReq) ProtoMessage() {}

func (x *SwapWithoutSignReq) ProtoReflect() protoreflect.Message {
	mi := &file_swap_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SwapWithoutSignReq.ProtoReflect.Descriptor instead.
func (*SwapWithoutSignReq) Descriptor() ([]byte, []int) {
	return file_swap_proto_rawDescGZIP(), []int{7}
}

func (x *SwapWithoutSignReq) GetSignerAddress() string {
	if x != nil {
		return x.SignerAddress
	}
	return ""
}

func (x *SwapWithoutSignReq) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *SwapWithoutSignReq) GetAct() SwapAct {
	if x != nil {
		return x.Act
	}
	return SwapAct_Buy
}

func (x *SwapWithoutSignReq) GetAmount() string {
	if x != nil {
		return x.Amount
	}
	return ""
}

func (x *SwapWithoutSignReq) GetSlippage() uint64 {
	if x != nil {
		return x.Slippage
	}
	return 0
}

func (x *SwapWithoutSignReq) GetGasFee() string {
	if x != nil {
		return x.GasFee
	}
	return ""
}

func (x *SwapWithoutSignReq) GetDexName() string {
	if x != nil {
		return x.DexName
	}
	return ""
}

func (x *SwapWithoutSignReq) GetChainID() uint64 {
	if x != nil {
		return x.ChainID
	}
	return 0
}

func (x *SwapWithoutSignReq) GetJitoTip() string {
	if x != nil {
		return x.JitoTip
	}
	return ""
}

func (x *SwapWithoutSignReq) GetFeeAddress() string {
	if x != nil {
		return x.FeeAddress
	}
	return ""
}

func (x *SwapWithoutSignReq) GetFeeRatio() uint64 {
	if x != nil {
		return x.FeeRatio
	}
	return 0
}

type SwapWithoutSignResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TxPayload     string                 `protobuf:"bytes,1,opt,name=txPayload,proto3" json:"txPayload,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SwapWithoutSignResp) Reset() {
	*x = SwapWithoutSignResp{}
	mi := &file_swap_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SwapWithoutSignResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SwapWithoutSignResp) ProtoMessage() {}

func (x *SwapWithoutSignResp) ProtoReflect() protoreflect.Message {
	mi := &file_swap_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SwapWithoutSignResp.ProtoReflect.Descriptor instead.
func (*SwapWithoutSignResp) Descriptor() ([]byte, []int) {
	return file_swap_proto_rawDescGZIP(), []int{8}
}

func (x *SwapWithoutSignResp) GetTxPayload() string {
	if x != nil {
		return x.TxPayload
	}
	return ""
}

var File_swap_proto protoreflect.FileDescriptor

var file_swap_proto_rawDesc = string([]byte{
	0x0a, 0x0a, 0x73, 0x77, 0x61, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x61, 0x70,
	0x69, 0x2e, 0x73, 0x77, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x22, 0x81, 0x01, 0x0a, 0x0f, 0x53, 0x69,
	0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x53, 0x77, 0x61, 0x70, 0x52, 0x65, 0x71, 0x12, 0x22, 0x0a,
	0x0c, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x12, 0x14, 0x0a,
	0x05, 0x69, 0x73, 0x42, 0x75, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x69, 0x73,
	0x42, 0x75, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x49, 0x44, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x49, 0x44, 0x22, 0x30, 0x0a,
	0x10, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x53, 0x77, 0x61, 0x70, 0x52, 0x65, 0x73,
	0x70, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x4f, 0x75, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x4f, 0x75, 0x74, 0x22,
	0x0e, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x47, 0x61, 0x73, 0x46, 0x65, 0x65, 0x52, 0x65, 0x71, 0x22,
	0x3c, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x47, 0x61, 0x73, 0x46, 0x65, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x12, 0x2b, 0x0a, 0x06, 0x67, 0x61, 0x73, 0x46, 0x65, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x13, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x77, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x61, 0x73, 0x46, 0x65, 0x65, 0x52, 0x06, 0x67, 0x61, 0x73, 0x46, 0x65, 0x65, 0x22, 0x70, 0x0a,
	0x06, 0x47, 0x61, 0x73, 0x46, 0x65, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x16, 0x0a,
	0x06, 0x67, 0x61, 0x73, 0x46, 0x65, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x67,
	0x61, 0x73, 0x46, 0x65, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x67, 0x61, 0x73, 0x46, 0x65, 0x65, 0x41,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x67, 0x61, 0x73,
	0x46, 0x65, 0x65, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22,
	0xe5, 0x02, 0x0a, 0x07, 0x53, 0x77, 0x61, 0x70, 0x52, 0x65, 0x71, 0x12, 0x20, 0x0a, 0x0b, 0x73,
	0x69, 0x67, 0x6e, 0x65, 0x72, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0b, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x72, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x24, 0x0a,
	0x0d, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x26, 0x0a, 0x03, 0x61, 0x63, 0x74,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x77, 0x61,
	0x70, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x77, 0x61, 0x70, 0x41, 0x63, 0x74, 0x52, 0x03, 0x61, 0x63,
	0x74, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x6c, 0x69,
	0x70, 0x70, 0x61, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x73, 0x6c, 0x69,
	0x70, 0x70, 0x61, 0x67, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x67, 0x61, 0x73, 0x46, 0x65, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x67, 0x61, 0x73, 0x46, 0x65, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x64, 0x65, 0x78, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x64, 0x65, 0x78, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x69, 0x6e,
	0x49, 0x44, 0x18, 0x09, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x49,
	0x44, 0x12, 0x18, 0x0a, 0x07, 0x6a, 0x69, 0x74, 0x6f, 0x54, 0x69, 0x70, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x6a, 0x69, 0x74, 0x6f, 0x54, 0x69, 0x70, 0x12, 0x1e, 0x0a, 0x0a, 0x66,
	0x65, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x66, 0x65, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x66,
	0x65, 0x65, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x66,
	0x65, 0x65, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x22, 0x22, 0x0a, 0x08, 0x53, 0x77, 0x61, 0x70, 0x52,
	0x65, 0x73, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x78, 0x48, 0x61, 0x73, 0x68, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x78, 0x48, 0x61, 0x73, 0x68, 0x22, 0xce, 0x02, 0x0a, 0x12,
	0x53, 0x77, 0x61, 0x70, 0x57, 0x69, 0x74, 0x68, 0x6f, 0x75, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x52,
	0x65, 0x71, 0x12, 0x24, 0x0a, 0x0d, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x69, 0x67, 0x6e, 0x65,
	0x72, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x26,
	0x0a, 0x03, 0x61, 0x63, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x73, 0x77, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x77, 0x61, 0x70, 0x41, 0x63,
	0x74, 0x52, 0x03, 0x61, 0x63, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1a,
	0x0a, 0x08, 0x73, 0x6c, 0x69, 0x70, 0x70, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x08, 0x73, 0x6c, 0x69, 0x70, 0x70, 0x61, 0x67, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x67, 0x61,
	0x73, 0x46, 0x65, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x67, 0x61, 0x73, 0x46,
	0x65, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x65, 0x78, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x64, 0x65, 0x78, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x63, 0x68, 0x61, 0x69, 0x6e, 0x49, 0x44, 0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x63,
	0x68, 0x61, 0x69, 0x6e, 0x49, 0x44, 0x12, 0x18, 0x0a, 0x07, 0x6a, 0x69, 0x74, 0x6f, 0x54, 0x69,
	0x70, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6a, 0x69, 0x74, 0x6f, 0x54, 0x69, 0x70,
	0x12, 0x1e, 0x0a, 0x0a, 0x66, 0x65, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x66, 0x65, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x12, 0x1a, 0x0a, 0x08, 0x66, 0x65, 0x65, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x08, 0x66, 0x65, 0x65, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x22, 0x33, 0x0a, 0x13,
	0x53, 0x77, 0x61, 0x70, 0x57, 0x69, 0x74, 0x68, 0x6f, 0x75, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x78, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x78, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61,
	0x64, 0x2a, 0x1c, 0x0a, 0x07, 0x53, 0x77, 0x61, 0x70, 0x41, 0x63, 0x74, 0x12, 0x07, 0x0a, 0x03,
	0x42, 0x75, 0x79, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x53, 0x65, 0x6c, 0x6c, 0x10, 0x01, 0x32,
	0xb1, 0x02, 0x0a, 0x0b, 0x53, 0x77, 0x61, 0x70, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12,
	0x44, 0x0a, 0x09, 0x47, 0x65, 0x74, 0x47, 0x61, 0x73, 0x46, 0x65, 0x65, 0x12, 0x19, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x73, 0x77, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x47, 0x61,
	0x73, 0x46, 0x65, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x77,
	0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x47, 0x61, 0x73, 0x46, 0x65, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x35, 0x0a, 0x04, 0x53, 0x77, 0x61, 0x70, 0x12, 0x14, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x73, 0x77, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x77, 0x61, 0x70,
	0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x77, 0x61, 0x70, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x77, 0x61, 0x70, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x4d, 0x0a, 0x0c,
	0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x53, 0x77, 0x61, 0x70, 0x12, 0x1c, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x73, 0x77, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x69, 0x6d, 0x75, 0x6c,
	0x61, 0x74, 0x65, 0x53, 0x77, 0x61, 0x70, 0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x73, 0x77, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74,
	0x65, 0x53, 0x77, 0x61, 0x70, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x56, 0x0a, 0x0f, 0x53,
	0x77, 0x61, 0x70, 0x57, 0x69, 0x74, 0x68, 0x6f, 0x75, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x12, 0x1f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x77, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x77, 0x61,
	0x70, 0x57, 0x69, 0x74, 0x68, 0x6f, 0x75, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x71, 0x1a,
	0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x77, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x77,
	0x61, 0x70, 0x57, 0x69, 0x74, 0x68, 0x6f, 0x75, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x22, 0x00, 0x42, 0x0a, 0x5a, 0x08, 0x2e, 0x2f, 0x3b, 0x74, 0x72, 0x61, 0x64, 0x65, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_swap_proto_rawDescOnce sync.Once
	file_swap_proto_rawDescData []byte
)

func file_swap_proto_rawDescGZIP() []byte {
	file_swap_proto_rawDescOnce.Do(func() {
		file_swap_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_swap_proto_rawDesc), len(file_swap_proto_rawDesc)))
	})
	return file_swap_proto_rawDescData
}

var file_swap_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_swap_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_swap_proto_goTypes = []any{
	(SwapAct)(0),                // 0: api.swap.v1.SwapAct
	(*SimulateSwapReq)(nil),     // 1: api.swap.v1.SimulateSwapReq
	(*SimulateSwapResp)(nil),    // 2: api.swap.v1.SimulateSwapResp
	(*GetGasFeeReq)(nil),        // 3: api.swap.v1.GetGasFeeReq
	(*GetGasFeeResp)(nil),       // 4: api.swap.v1.GetGasFeeResp
	(*GasFee)(nil),              // 5: api.swap.v1.GasFee
	(*SwapReq)(nil),             // 6: api.swap.v1.SwapReq
	(*SwapResp)(nil),            // 7: api.swap.v1.SwapResp
	(*SwapWithoutSignReq)(nil),  // 8: api.swap.v1.SwapWithoutSignReq
	(*SwapWithoutSignResp)(nil), // 9: api.swap.v1.SwapWithoutSignResp
}
var file_swap_proto_depIdxs = []int32{
	5, // 0: api.swap.v1.GetGasFeeResp.gasFee:type_name -> api.swap.v1.GasFee
	0, // 1: api.swap.v1.SwapReq.act:type_name -> api.swap.v1.SwapAct
	0, // 2: api.swap.v1.SwapWithoutSignReq.act:type_name -> api.swap.v1.SwapAct
	3, // 3: api.swap.v1.SwapService.GetGasFee:input_type -> api.swap.v1.GetGasFeeReq
	6, // 4: api.swap.v1.SwapService.Swap:input_type -> api.swap.v1.SwapReq
	1, // 5: api.swap.v1.SwapService.SimulateSwap:input_type -> api.swap.v1.SimulateSwapReq
	8, // 6: api.swap.v1.SwapService.SwapWithoutSign:input_type -> api.swap.v1.SwapWithoutSignReq
	4, // 7: api.swap.v1.SwapService.GetGasFee:output_type -> api.swap.v1.GetGasFeeResp
	7, // 8: api.swap.v1.SwapService.Swap:output_type -> api.swap.v1.SwapResp
	2, // 9: api.swap.v1.SwapService.SimulateSwap:output_type -> api.swap.v1.SimulateSwapResp
	9, // 10: api.swap.v1.SwapService.SwapWithoutSign:output_type -> api.swap.v1.SwapWithoutSignResp
	7, // [7:11] is the sub-list for method output_type
	3, // [3:7] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_swap_proto_init() }
func file_swap_proto_init() {
	if File_swap_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_swap_proto_rawDesc), len(file_swap_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_swap_proto_goTypes,
		DependencyIndexes: file_swap_proto_depIdxs,
		EnumInfos:         file_swap_proto_enumTypes,
		MessageInfos:      file_swap_proto_msgTypes,
	}.Build()
	File_swap_proto = out.File
	file_swap_proto_goTypes = nil
	file_swap_proto_depIdxs = nil
}
