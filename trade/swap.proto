syntax = "proto3";

package api.swap.v1;

option go_package = "./;trade";

service SwapService {
  rpc GetGasFee (GetGasFeeReq) returns (GetGasFeeResp) {};

  // SwapWithSign 签名并广播，返回交易哈希
  rpc Swap (SwapReq) returns (SwapResp) {};

  // SimulateSwap 模拟交易，返回交易结果
  rpc SimulateSwap (SimulateSwapReq) returns (SimulateSwapResp) {};

  // SwapWithoutSign 构建交易不签名，返回交易负载
  rpc SwapWithoutSign (SwapWithoutSignReq) returns (SwapWithoutSignResp) {};

}

message SimulateSwapReq{
  string tokenAddress = 1;
  string amountIn = 2;
  bool isBuy = 3;
  uint64 chainID = 4;
}
message SimulateSwapResp{
  string amountOut = 1;
}

message GetGasFeeReq{
}

message GetGasFeeResp {
  repeated GasFee gasFee = 1;
}

message GasFee{
  int64 level = 1;
  double gasFee = 2;
  string gasFeeAmount = 3;
  double value = 4;
}

message SwapReq{
  int64 signerIndex = 1; //签名地址的 index
  string signerAddress = 2; //签名地址
  string token = 3; //token 地址
  SwapAct act = 4; //动作
  string  amount = 5; //数量
  uint64 slippage = 6;
  string gasFee = 7;
  string dexName = 8;
  uint64 chainID = 9;
  string jitoTip = 10;
  string feeAddress = 11;
  uint64 feeRatio = 12;
}

message SwapResp{
  string txHash = 1;
}

message SwapWithoutSignReq{
  string signerAddress = 1; //签名地址
  string token = 2; //token 地址
  SwapAct act = 3; //动作
  string  amount = 4; //数量
  uint64 slippage = 5;
  string gasFee = 6;
  string dexName = 7;
  uint64 chainID = 8;
  string jitoTip = 9;
  string feeAddress = 10;
  uint64 feeRatio = 11;
}

message SwapWithoutSignResp{
  string txPayload = 1;
}

enum SwapAct {
  Buy = 0;
  Sell = 1;
}