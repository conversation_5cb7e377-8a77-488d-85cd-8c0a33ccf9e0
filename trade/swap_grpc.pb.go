// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: swap.proto

package trade

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	SwapService_GetGasFee_FullMethodName       = "/api.swap.v1.SwapService/GetGasFee"
	SwapService_Swap_FullMethodName            = "/api.swap.v1.SwapService/Swap"
	SwapService_SimulateSwap_FullMethodName    = "/api.swap.v1.SwapService/SimulateSwap"
	SwapService_SwapWithoutSign_FullMethodName = "/api.swap.v1.SwapService/SwapWithoutSign"
)

// SwapServiceClient is the client API for SwapService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SwapServiceClient interface {
	GetGasFee(ctx context.Context, in *GetGasFeeReq, opts ...grpc.CallOption) (*GetGasFeeResp, error)
	// SwapWithSign 签名并广播，返回交易哈希
	Swap(ctx context.Context, in *SwapReq, opts ...grpc.CallOption) (*SwapResp, error)
	// SimulateSwap 模拟交易，返回交易结果
	SimulateSwap(ctx context.Context, in *SimulateSwapReq, opts ...grpc.CallOption) (*SimulateSwapResp, error)
	// SwapWithoutSign 构建交易不签名，返回交易负载
	SwapWithoutSign(ctx context.Context, in *SwapWithoutSignReq, opts ...grpc.CallOption) (*SwapWithoutSignResp, error)
}

type swapServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSwapServiceClient(cc grpc.ClientConnInterface) SwapServiceClient {
	return &swapServiceClient{cc}
}

func (c *swapServiceClient) GetGasFee(ctx context.Context, in *GetGasFeeReq, opts ...grpc.CallOption) (*GetGasFeeResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetGasFeeResp)
	err := c.cc.Invoke(ctx, SwapService_GetGasFee_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *swapServiceClient) Swap(ctx context.Context, in *SwapReq, opts ...grpc.CallOption) (*SwapResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SwapResp)
	err := c.cc.Invoke(ctx, SwapService_Swap_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *swapServiceClient) SimulateSwap(ctx context.Context, in *SimulateSwapReq, opts ...grpc.CallOption) (*SimulateSwapResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SimulateSwapResp)
	err := c.cc.Invoke(ctx, SwapService_SimulateSwap_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *swapServiceClient) SwapWithoutSign(ctx context.Context, in *SwapWithoutSignReq, opts ...grpc.CallOption) (*SwapWithoutSignResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SwapWithoutSignResp)
	err := c.cc.Invoke(ctx, SwapService_SwapWithoutSign_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SwapServiceServer is the server API for SwapService service.
// All implementations must embed UnimplementedSwapServiceServer
// for forward compatibility.
type SwapServiceServer interface {
	GetGasFee(context.Context, *GetGasFeeReq) (*GetGasFeeResp, error)
	// SwapWithSign 签名并广播，返回交易哈希
	Swap(context.Context, *SwapReq) (*SwapResp, error)
	// SimulateSwap 模拟交易，返回交易结果
	SimulateSwap(context.Context, *SimulateSwapReq) (*SimulateSwapResp, error)
	// SwapWithoutSign 构建交易不签名，返回交易负载
	SwapWithoutSign(context.Context, *SwapWithoutSignReq) (*SwapWithoutSignResp, error)
	mustEmbedUnimplementedSwapServiceServer()
}

// UnimplementedSwapServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedSwapServiceServer struct{}

func (UnimplementedSwapServiceServer) GetGasFee(context.Context, *GetGasFeeReq) (*GetGasFeeResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGasFee not implemented")
}
func (UnimplementedSwapServiceServer) Swap(context.Context, *SwapReq) (*SwapResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Swap not implemented")
}
func (UnimplementedSwapServiceServer) SimulateSwap(context.Context, *SimulateSwapReq) (*SimulateSwapResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SimulateSwap not implemented")
}
func (UnimplementedSwapServiceServer) SwapWithoutSign(context.Context, *SwapWithoutSignReq) (*SwapWithoutSignResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SwapWithoutSign not implemented")
}
func (UnimplementedSwapServiceServer) mustEmbedUnimplementedSwapServiceServer() {}
func (UnimplementedSwapServiceServer) testEmbeddedByValue()                     {}

// UnsafeSwapServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SwapServiceServer will
// result in compilation errors.
type UnsafeSwapServiceServer interface {
	mustEmbedUnimplementedSwapServiceServer()
}

func RegisterSwapServiceServer(s grpc.ServiceRegistrar, srv SwapServiceServer) {
	// If the following call pancis, it indicates UnimplementedSwapServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&SwapService_ServiceDesc, srv)
}

func _SwapService_GetGasFee_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGasFeeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SwapServiceServer).GetGasFee(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SwapService_GetGasFee_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SwapServiceServer).GetGasFee(ctx, req.(*GetGasFeeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SwapService_Swap_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SwapReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SwapServiceServer).Swap(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SwapService_Swap_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SwapServiceServer).Swap(ctx, req.(*SwapReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SwapService_SimulateSwap_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SimulateSwapReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SwapServiceServer).SimulateSwap(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SwapService_SimulateSwap_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SwapServiceServer).SimulateSwap(ctx, req.(*SimulateSwapReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SwapService_SwapWithoutSign_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SwapWithoutSignReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SwapServiceServer).SwapWithoutSign(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SwapService_SwapWithoutSign_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SwapServiceServer).SwapWithoutSign(ctx, req.(*SwapWithoutSignReq))
	}
	return interceptor(ctx, in, info, handler)
}

// SwapService_ServiceDesc is the grpc.ServiceDesc for SwapService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SwapService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.swap.v1.SwapService",
	HandlerType: (*SwapServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetGasFee",
			Handler:    _SwapService_GetGasFee_Handler,
		},
		{
			MethodName: "Swap",
			Handler:    _SwapService_Swap_Handler,
		},
		{
			MethodName: "SimulateSwap",
			Handler:    _SwapService_SimulateSwap_Handler,
		},
		{
			MethodName: "SwapWithoutSign",
			Handler:    _SwapService_SwapWithoutSign_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "swap.proto",
}
