#! /bin/sh

# 定义镜像名称和标签
IMAGE_NAME="protobuf-builder"
TAG="v25.4.21"
FULL_IMAGE="${IMAGE_NAME}:${TAG}"

# 检查镜像是否存在
if ! docker image inspect "$FULL_IMAGE" >/dev/null 2>&1; then
  echo "镜像 $FULL_IMAGE 不存在，开始构建..."
  docker build -t "$FULL_IMAGE" -f ../Dockerfile .
else
  echo "镜像 $FULL_IMAGE 已存在，跳过构建"
fi

# 运行容器并执行命令
echo "start to run container ..."
docker run --rm --name protobuf-generator -v $(pwd):/app $FULL_IMAGE
echo "gen done!"
