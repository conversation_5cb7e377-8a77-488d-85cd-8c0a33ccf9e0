package mock

import (
	"context"

	"gitlab.jcwork.net/cedefi/cedefi-rpc/trade"

	"google.golang.org/grpc"
)

// SwapMockService 是 SwapService 的模拟实现
type SwapMockService struct {
	trade.UnimplementedSwapServiceServer
}

func (s *SwapMockService) GetGasFee(ctx context.Context, req *trade.GetGasFeeReq) (*trade.GetGasFeeResp, error) {
	return &trade.GetGasFeeResp{
		GasFee: []*trade.GasFee{
			{
				Level:        1,
				GasFee:       0.0015,
				GasFeeAmount: "150",
				Value:        1.1,
			},
			{
				Level:        2,
				GasFee:       0.005,
				GasFeeAmount: "500",
				Value:        1.82,
			},
			{
				Level:        3,
				GasFee:       0.0075,
				GasFeeAmount: "750",
				Value:        2.82,
			},
		},
	}, nil
}

func RegisterSwapMockService(server *grpc.Server) {
	trade.RegisterSwapServiceServer(server, &SwapMockService{})
}
